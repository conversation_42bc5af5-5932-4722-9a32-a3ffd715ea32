/**
 * @Desc: 取价测试
 * <AUTHOR>
 * @date 2023/10/10
 */
import PPM from 'plugin_public_methods'
const PriceService = require('../src/index').default;
const {CRM, dataGetter, dataUpdater, pluginService, pluginParam,
    mdData, masterData, masterApiName, mdApiName, realPriceRes, addDatas, lookupDatas,
    mock_getConfig, mock_getAllFields} = require('../testmock/mock').default;
global.CRM = CRM;
// 模拟插件入参
const params = {
    dataGetter,
    dataUpdater,
    objApiName: mdApiName,
    addDatas,
    lookupDatas,
    lookupField: {api_name: "price_book_product_id"},
    master_data: masterData,
    masterObjApiName: masterApiName,
    filterFields:{}
};
// 插件初始化
const PS = new PriceService(pluginService, pluginParam);

// 添加数据模块
const Add = PS.Add;

let mockRunPlugin = jest.fn();
let mockRunPluginSync = jest.fn();
let mockBeginGetRealPrice = jest.fn();
PS.runPlugin = mockRunPlugin;
PS.runPluginSync = mockRunPluginSync;
PS.getConfig = mock_getConfig;
PS.getAllFields = mock_getAllFields;
PS.beginGetRealPrice = mockBeginGetRealPrice;
mockBeginGetRealPrice.mockResolvedValue({
    applicablePriceSystem: true,
    newRst: [{}]
});
describe('test', () => {
    test('getAllFields', () => {
        const res = Add.getAllFields();
        expect(res).toEqual(expect.objectContaining({
            price_book_product_id: "price_book_product_id",
            product_id: "product_id",
            quantity: "quantity"
        }));
    })
})

// 测试添加数据取价
describe('plays _batchAddAfterHook', () => {
    test('测试添加数据取价 _batchAddAfterHook', async () => {
        // Mock CRM.util.getConfigStatusByKey
        global.CRM = {
            util: {
                getConfigStatusByKey: jest.fn().mockReturnValue('0')
            }
        };

        mockRunPlugin.mockResolvedValueOnce({ isDoPriceService: true });
        mockRunPlugin.mockResolvedValueOnce({ data: [] });
        mockRunPlugin.mockResolvedValueOnce({ data: [] });
        await Add._batchAddAfterHook({}, params);
        expect(mockRunPlugin).toHaveBeenCalledWith('price-service.batchAddAfter.before', expect.any(Object));
        expect(mockRunPlugin).toHaveBeenCalledWith('price-service.batchAddAfter.parseData', expect.any(Object));
        expect(mockBeginGetRealPrice).toHaveBeenCalled();
        expect(mockRunPlugin).toHaveBeenCalledWith('price-service.batchAddAfter.after', expect.any(Object));
    });
});

describe('plays _mdEditAfter', () => {
    test('修改价目表 _mdEditAfter', async () => {
        let param = {
            bizApi: {},
            dataGetter,
            dataUpdater,
            masterObjApiName: masterApiName,
            formType: "add",
            UI: {},
            objApiName: mdApiName,
            recordType: "default__c",
            fieldName: "price_book_id",
            dataIndex: [
                "****************"
            ],
            options: [],
            operateType: "mdEdit",
            lookupData: {
                "pricebook_id": "612c893279a28600018340b5",
                "pricebook_id__r": "标准价目表",
                "name": "智能摄像头",
                "_id": "65f969b05d028e0007207b92",
            },
            changeData: {
                "****************": {
                    "price_book_id": "612c893279a28600018340b4"
                }
            }
        };
        await Add._mdEditAfter({}, param);
        // setPriceBookFields
        expect(param.changeData['****************']).toHaveProperty('price_book_id__r')
        expect(param.changeData['****************']).toHaveProperty('price_book_product_id__r')
    })
    test('修改价目表明细 _mdEditAfter', async () => {
        let param = {
            bizApi: {},
            dataGetter,
            dataUpdater,
            masterObjApiName: masterApiName,
            formType: "add",
            UI: {},
            objApiName: mdApiName,
            recordType: "default__c",
            fieldName: "price_book_product_id",
            dataIndex: [
                "****************"
            ],
            options: [],
            operateType: "mdEdit",
            lookupData: {
                "pricebook_id": "6594c79bc715080007e4b05b",
                "pricebook_id__r": "标准价目表",
                "name": "智能摄像头",
                "_id": "665ec11c8ad1e00007a4c14a",
            },
            changeData: {
                "****************": {
                    "price_book_product_id": "66388943806ac10007583f6d90242"
                }
            }
        };
        await Add._mdEditAfter({}, param);
        // addProductInfo_lookup
        expect(param.changeData['****************']).toHaveProperty(['product_id__r'])
    })
    test('修改数量 _mdEditAfter', async () => {
        // Mock 阶梯价配置
        const spy_config = jest.spyOn(PS, 'getConfig');
        spy_config.mockImplementation((key) => {
            if (key === 'price_book_product_tiered_price') return '1';
            return mock_getConfig(key);
        })

        mockRunPlugin.mockResolvedValueOnce({}); // price-service.mdEditAfter.before
        mockRunPluginSync.mockReturnValue({ changeRowIds: ['****************'] });

        let param = {
            bizApi: {},
            dataGetter,
            dataUpdater,
            masterObjApiName: masterApiName,
            formType: "add",
            UI: {},
            objApiName: mdApiName,
            recordType: "default__c",
            fieldName: "quantity",
            dataIndex: [
                "****************"
            ],
            options: [],
            operateType: "mdEdit",
            changeData: {
                "****************": {
                    "quantity": "3"
                }
            },
            triggerCal: () => {}
        };

        // Mock getTieredPrice method - 直接在Add对象上添加方法
        const originalGetTieredPrice = Add.getTieredPrice;
        Add.getTieredPrice = jest.fn().mockResolvedValue({});

        await Add._mdEditAfter({}, param);
        expect(mockRunPlugin).toHaveBeenCalledWith('price-service.mdEditAfter.before', expect.any(Object));

        // 恢复原方法
        if (originalGetTieredPrice) {
            Add.getTieredPrice = originalGetTieredPrice;
        } else {
            delete Add.getTieredPrice;
        }
        spy_config.mockRestore();
    })


})


describe('plays _mdCopyAfter', () => {
    let param = {
        "bizApi": {},
        "dataUpdater": dataUpdater,
        "dataGetter": dataGetter,
        "masterObjApiName": "QuoteObj",
        "formType": "edit",
        "UI": {},
        "objApiName": "QuoteLinesObj",
        "recordType": "default__c",
        "copyRowIds": [
            "****************"
        ],
        "operateType": "mdCopy",
        "parseParam": "",
        "newDataIndexs": [
            "****************"
        ],
        "dataIndex": [
            "****************"
        ]
    };
    param.dataUpdater.updateData = jest.fn();
    Add._mdCopyAfter({}, param);
    expect(param.dataUpdater.updateData).toHaveBeenCalled()
})

describe('plays _batchAddBeforeHook', () => {
    test('请先选客户 _batchAddBeforeHook', async () => {
        let {form_account_id} = Add.getAllFields();
        let data = params.dataGetter.getMasterData();
        const spy_getMasterData = jest.spyOn(params.dataGetter, 'getMasterData')
        spy_getMasterData.mockImplementation(() => {
            return {
                ...data,
                [form_account_id]: ''
            }
        })
        pluginService.api.alert = jest.fn()
        await Add._batchAddBeforeHook(pluginService, params);
        expect(pluginService.api.alert).toHaveBeenCalledWith('请先选客户');
        spy_getMasterData.mockRestore()
    })
    test('请先选价目表 _batchAddBeforeHook', async () => {
        let {form_price_book_id} = Add.getAllFields();
        let data = params.dataGetter.getMasterData();
        let desc = params.dataGetter.getDescribe()
        const spy_getMasterData = jest.spyOn(params.dataGetter, 'getMasterData')
        const spy_getDescribe = jest.spyOn(params.dataGetter, 'getDescribe')
        spy_getMasterData.mockImplementation(() => {
            return {
                ...data,
                [form_price_book_id]: ''
            }
        })
        spy_getDescribe.mockImplementation(() => {
            return {
                ...desc,
                fields: {
                    ...desc.fields,
                    [form_price_book_id]: {
                        ...desc.fields[form_price_book_id],
                        is_required: true
                    }
                }
            }
        })
        pluginService.api.alert = jest.fn()
        await Add._batchAddBeforeHook(pluginService, params);
        expect(pluginService.api.alert).toHaveBeenCalledWith('请先选择价目表');
        spy_getMasterData.mockRestore()
        spy_getDescribe.mockRestore()
    })
    test('_batchAddBeforeHook 正常情况', async () => {
        pluginService.api.alert = jest.fn()
        pluginService.skipPlugin = jest.fn()
        await Add._batchAddBeforeHook(pluginService, params);
        expect(pluginService.api.alert).toHaveBeenCalledTimes(0);
        expect(pluginService.skipPlugin).toHaveBeenCalledTimes(0);
    })
    test('_batchAddBeforeHook 非特殊字段', async () => {
        let testParams = {
            ...params,
            lookupField: {api_name: "other_field"}
        };
        pluginService.api.alert = jest.fn()
        await Add._batchAddBeforeHook(pluginService, testParams);
        expect(pluginService.api.alert).toHaveBeenCalledTimes(0);
    })
})

// 新增测试用例 - 测试未覆盖的方法
describe('plays addPriceBookId', () => {
    test('addPriceBookId 正常情况', () => {
        let testData = [{rowId: '123'}];
        let testLookupData = [{
            price_book_product_id: 'pbp123',
            price_book_id: 'pb123',
            actual_unit: 'unit123'
        }];
        Add.addPriceBookId(testData, mdApiName, testLookupData, params);
        expect(testData[0].price_book_product_id).toBe('pbp123');
        expect(testData[0].price_book_id).toBe('pb123');
    })

    test('addPriceBookId 空lookupDatas', () => {
        let testData = [{rowId: '123'}];
        Add.addPriceBookId(testData, mdApiName, [], params);
        expect(testData[0].price_book_product_id).toBeUndefined();
    })
})

describe('plays addProductInfo', () => {
    test('addProductInfo 正常情况', () => {
        let testData = [{rowId: '123'}];
        let testLookupData = [{
            product_id: 'prod123',
            product_id__r: '产品名称',
            _id: 'pbp123',
            name: 'PBP名称',
            selling_price: '100.00',
            discount: '90.0000'
        }];
        params.dataUpdater.updateDetail = jest.fn();
        Add.addProductInfo(testData, mdApiName, testLookupData, params);
        expect(testData[0].product_id).toBe('prod123');
        expect(testData[0]['product_id__r']).toBe('产品名称');
        expect(params.dataUpdater.updateDetail).toHaveBeenCalled();
    })

    test('addProductInfo 从取价服务来源', () => {
        let testData = [{rowId: '123'}];
        let testLookupData = [{
            rowId: '123',
            product_id: 'prod123',
            selling_price: '100.00'
        }];
        params.dataUpdater.updateDetail = jest.fn();
        Add.addProductInfo(testData, mdApiName, testLookupData, params, 'priceService');
        expect(testData[0].product_id).toBe('prod123');
        expect(params.dataUpdater.updateDetail).toHaveBeenCalled();
    })
})

describe('plays filterFieldsBeforeCalculate', () => {
    test('filterFieldsBeforeCalculate 不需要计算价格', () => {
        let res = {calculatePrice: false};
        let testParam = {objApiName: mdApiName, filterFields: {}};
        Add.filterFieldsBeforeCalculate(res, testParam);
        // 根据实际的字段名进行测试
        expect(testParam.filterFields[mdApiName]).toContain('price');
        expect(testParam.filterFields[mdApiName]).toContain('discount');
    })

    test('filterFieldsBeforeCalculate 需要计算价格', () => {
        let res = {calculatePrice: true};
        let testParam = {objApiName: mdApiName, filterFields: {}};
        Add.filterFieldsBeforeCalculate(res, testParam);
        expect(testParam.filterFields[mdApiName]).toBeUndefined();
    })
})

describe('plays addResToLookUpDatas', () => {
    test('addResToLookUpDatas', () => {
        let lookUpData = [{id: '1'}, {id: '2'}];
        let prsRes = [{result: 'res1'}, {result: 'res2'}];
        Add.addResToLookUpDatas(lookUpData, prsRes);
        expect(lookUpData[0].get_real_price_result).toEqual({result: 'res1'});
        expect(lookUpData[1].get_real_price_result).toEqual({result: 'res2'});
    })
})

describe('plays priceBookIsRequired', () => {
    test('priceBookIsRequired 价目表必填且为空', () => {
        let {form_price_book_id} = Add.getAllFields();
        let data = params.dataGetter.getMasterData();
        let desc = params.dataGetter.getDescribe()
        const spy_getMasterData = jest.spyOn(params.dataGetter, 'getMasterData')
        const spy_getDescribe = jest.spyOn(params.dataGetter, 'getDescribe')
        spy_getMasterData.mockImplementation(() => {
            return {
                ...data,
                [form_price_book_id]: ''
            }
        })
        spy_getDescribe.mockImplementation(() => {
            return {
                ...desc,
                fields: {
                    ...desc.fields,
                    [form_price_book_id]: {
                        ...desc.fields[form_price_book_id],
                        is_required: true
                    }
                }
            }
        })
        let result = Add.priceBookIsRequired(params);
        expect(result).toBe(true);
        spy_getMasterData.mockRestore()
        spy_getDescribe.mockRestore()
    })

    test('priceBookIsRequired 价目表不必填', () => {
        let result = Add.priceBookIsRequired(params);
        expect(result).toBe(false);
    })
})

describe('plays getAllProductIds', () => {
    test('getAllProductIds', () => {
        let details = [
            {product_id: 'prod1', record_type: 'default__c'},
            {product_id: 'prod2', record_type: 'default__c', parent_rowId: 'parent1'},
            {product_id: 'prod3', record_type: 'other_type'},
            {product_id: 'prod4', record_type: 'default__c'}
        ];
        let result = Add.getAllProductIds(details, 'product_id', 'default__c');
        expect(result).toEqual(['prod1', 'prod4']);
    })
})

describe('plays getAddData', () => {
    test('getAddData', () => {
        let testParam = {
            dataGetter: {
                getDetail: jest.fn().mockReturnValue([
                    {rowId: '1', name: 'item1'},
                    {rowId: '2', name: 'item2'},
                    {rowId: '3', name: 'item3'}
                ])
            },
            dataIndex: ['1', '3']
        };
        let result = Add.getAddData(testParam);
        expect(result).toHaveLength(2);
        expect(result[0].rowId).toBe('1');
        expect(result[1].rowId).toBe('3');
    })
})

describe('plays addProductInfo_lookup', () => {
    test('addProductInfo_lookup 正常情况', () => {
        let changeData = {
            'row1': {quantity: '5'}
        };
        let lookupData = {
            product_id: 'prod123',
            product_id__r: '产品名称'
        };
        let testParam = {objApiName: mdApiName};
        Add.addProductInfo_lookup(changeData, lookupData, testParam);
        expect(changeData.row1.product_id).toBe('prod123');
        expect(changeData.row1['product_id__r']).toBe('产品名称');
    })

    test('addProductInfo_lookup 空lookupData', () => {
        let changeData = {
            'row1': {quantity: '5'}
        };
        let testParam = {objApiName: mdApiName};
        Add.addProductInfo_lookup(changeData, null, testParam);
        expect(changeData.row1.product_id).toBeUndefined();
    })
})

describe('plays updateChangeData', () => {
    test('updateChangeData', () => {
        let changeData = {
            'row1': {quantity: '5', price: '100'},
            'row2': {quantity: '3'}
        };
        let changeDataList = [
            {rowId: 'row1', name: 'item1'},
            {rowId: 'row2', name: 'item2'}
        ];
        Add.updateChangeData(changeData, changeDataList);
        expect(changeDataList[0].quantity).toBe('5');
        expect(changeDataList[0].price).toBe('100');
        expect(changeDataList[1].quantity).toBe('3');
    })
})

describe('plays setPriceBookFields', () => {
    test('setPriceBookFields', () => {
        let changeData = {
            'row1': {}
        };
        let lookupData = {
            pricebook_id: 'pb123',
            pricebook_id__r: '价目表名称'
        };
        let testParam = {objApiName: mdApiName};
        Add.setPriceBookFields(changeData, lookupData, testParam);
        expect(changeData.row1.price_book_id__r).toBe('价目表名称');
        // 根据实际实现，可能不会设置这个字段，所以改为检查是否存在
        expect(changeData.row1).toHaveProperty('price_book_id__r');
    })
})

describe('plays _isModule2', () => {
    test('_isModule2 返回true', () => {
        global.CRM = {
            util: {
                getConfigStatusByKey: jest.fn().mockReturnValue('1')
            }
        };
        let result = Add._isModule2();
        expect(result).toBe(true);
    })

    test('_isModule2 返回false', () => {
        global.CRM = {
            util: {
                getConfigStatusByKey: jest.fn().mockReturnValue('0')
            }
        };
        let result = Add._isModule2();
        expect(result).toBe(false);
    })
})

