/*
 * @Author: lishsh9516
 * @Date: 2024-06-Fr 06:02:46
 * @Last Modified by:   lishsh9516
 * @Last Modified time: 2024-06-Fr 06:02:46
 */

import PPM from 'plugin_public_methods'
const PriceService = require('../src/index').default;
const {dataGetter, dataUpdater, pluginService, pluginParam,
    mdData, masterData, masterApiName, mdApiName, realPriceRes,
    addDatas, lookupDatas, detailData, CRM,
    mock_getConfig, defaultConfig, mock_getAllFields} = require('../testmock/mock').default;
    global.CRM = CRM;

// 插件初始化
const PS = new PriceService(pluginService, pluginParam);
const NoClearMd = PS.NoClearMd;
let mockRunPlugin = jest.fn();
let mockRunPluginSync = jest.fn();
PS.runPlugin = mockRunPlugin;
PS.runPluginSync = mockRunPluginSync;
PS.getConfig = mock_getConfig;
PS.getAllFields = mock_getAllFields;
const mockGetMasterFields = jest.fn()
PS.getMasterFields = mockGetMasterFields
mockGetMasterFields.mockReturnValue({
    "form_account_id": "account_id",
    "form_partner_id": "partner_id",
    "form_price_book_id": "price_book_id",
    "form_mc_currency": "mc_currency"
})
// validPriceBookByCustomer
const spy_validPriceBookByCustomer = jest.spyOn(NoClearMd, 'validPriceBookByCustomer');
spy_validPriceBookByCustomer.mockImplementation(() => {
    return {
        "result": true
    }
})
// getRealPrice
const spy = jest.spyOn(PS, 'getRealPrice');
spy.mockImplementation(() => {
    return {
        newRst: realPriceRes,
        applicablePriceSystem: true,
        calculatePrice: true,
    }
});
// triggerCalAndUIEvent triggerCal
const triggerCal = jest.fn();
const triggerCalAndUIEvent = jest.fn();
triggerCalAndUIEvent.mockResolvedValueOnce({
    "Result": {
        "FailureCode": 0,
        "StatusCode": 0,
    },
    "Value": {
        "calculateResult": {
            "QuoteLinesObj": {
                "****************": {
                  "selling_price": "10.00",
                  "base_sales_price": "10.00",
                  "base_total_amount": "30.00",
                  "total_discount": "100.0000",
                  "field_D45J6__c": "1",
                  "base_sales_amount": "30.00",
                  "discount": "100.0000",
                  "sales_amount": "30.00",
                  "total_amount": "30.00",
                  "price": "10.00",
                  "sales_price": "10.00",
                  "base_price": "10.00",
                  "base_selling_price": "10.00"
                },
              },
            "QuoteObj": {
                "0": {
                  "quote_amount": "30.00",
                  "quote_product_sum": "30.00",
                  "base_quote_product_sum": "30.000",
                  "base_quote_amount": "30.00"
                }
              }
        }
    }
})
// confirm
const spy_confirm = jest.spyOn(pluginService.api, 'confirm');
spy_confirm.mockImplementation(async ({title, msg, success = jest.fn(), cancel = jest.fn()}) => {
    const result2 = await cancel('mocked cancel result2');
    const result = await success('mocked result');
})
// updateMaster
const spy_updateMaster = jest.spyOn(dataUpdater, 'updateMaster');
spy_updateMaster.mockImplementation((data) => {
    // console.log('play updateMaster')
    return data;
});
describe('play noClearMd', () => {
    test('noClearMd 开价目表且没开价格政策', () => {
        let flag = NoClearMd.noClearMd(pluginService);
        expect(flag).toBe(true)
    })
    test('noClearMd 不开价目表且没开价格政策', () => {
        const spy_config1 = jest.spyOn(PS, 'getConfig');
        spy_config1.mockImplementation((key) => {
            let _config = {
                ...defaultConfig,
                openPriceList: false
            }
            return _config[key];
        })
        let flag = NoClearMd.noClearMd(pluginService);
        expect(flag).toBe(false)
        spy_config1.mockRestore();
    })
    test('noClearMd 开价目表且开价格政策', () => {
        const spy_pluginService = jest.spyOn(pluginService.api, 'getPlugins');
        spy_pluginService.mockImplementation(() => {
            return [{
                pluginApiName: 'price_policy'
            }]
        })
        let flag = NoClearMd.noClearMd(pluginService);
        expect(flag).toBe(false)
        spy_pluginService.mockRestore()
    })
})
describe('play1 _dataChange_after 切换价目表字段', () => {
    let param = {
        bizApi: {},
        dataUpdater,
        dataGetter,
        masterObjApiName: 'QuoteObj',
        formType: "edit",
        UI: {},
        changeData: {
            price_book_id: "66597bc3ebd8080007bac1f0"

        },
        oldData: {
            "price_book_id": "6594c79bc715080007e4b05b",
            "price_book_id__r": "标准价目表"
        },
        autoFields: {},
        isManual: true,
        __eventKey: "form.dataChange.after",
        triggerCal,
        triggerCalAndUIEvent
    }


    let {form_price_book_id, form_account_id} = PS.getAllFields();
    let field = NoClearMd.findChangeField({changeData: param.changeData, masterApiName: param.masterObjApiName}, param)
    let value = param.changeData[field];
    let allOldData = param.oldData;

    test('_dataChange_after', () => {
        NoClearMd._dataChange_after(pluginService, param);
    })
    test('findChangeField', () => {
        // 切换字段
        expect(field).toEqual('price_book_id')
    })
    test('checkValue 切换价目表字段', () => {
        let r = NoClearMd.checkValue({
            key: field,
            value,
            allOldData,
            masterData
        }, param)
        expect(r).toBe(true)
    })

    test('todoAfterFieldChange 切换主对象价目表字段', async () => {
        let resolve = jest.fn();
        let spy = jest.spyOn(NoClearMd, 'checkMdIsMatchCustomer');
        await NoClearMd.todoAfterFieldChange(field, param, pluginService, resolve)
        expect(spy).toHaveBeenCalled();
        spy.mockRestore();

    })

})
describe('play2 _dataChange_after 切换客户字段', () => {
    let param = {
        bizApi: {},
        dataUpdater,
        dataGetter,
        masterObjApiName: "QuoteObj",
        formType: "edit",
        UI: {},
        changeData: {
            "account_id": "66050ef13ce8640007403220",
            "price_book_id": ""
        },
        oldData: {
            "account_id": "65966cddc6f30e0007f8ec94",
            "account_id__r": "李四",
            "price_book_id": "66597bc3ebd8080007bac1f0",
            "price_book_id__r": "阶梯价目表"
        },
        autoFields: {},
        isManual: true,
        triggerCal,
        triggerCalAndUIEvent
    }
    const resolve = jest.fn();
    let {form_price_book_id, form_account_id} = PS.getAllFields();
    let field = NoClearMd.findChangeField({changeData: param.changeData, masterApiName: param.masterObjApiName}, param)
    let value = param.changeData[field];
    let allOldData = param.oldData;
    test('findChangeField', () => {
        // 切换字段
        expect(field).toEqual('account_id')
    })
    test('checkValue 切换客户字段', () => {
        // 切换客户字段
        let r = NoClearMd.checkValue({
            key: field,
            value,
            allOldData,
            masterData
        }, param)
        expect(r).toBe(true);
    })
    test('todoAfterFieldChange 切换客户字段', async () => {
        // 切换主对象客户
        let spy = jest.spyOn(NoClearMd, 'checkMdIsMatchCustomer');

        await NoClearMd.todoAfterFieldChange(field, param, pluginService, resolve)
        let priceBookId = allOldData[form_price_book_id] || masterData[form_price_book_id];
        let priceBookId__r = allOldData[form_price_book_id + '__r'] || masterData[form_price_book_id + '__r'];

        expect(spy_updateMaster).toHaveBeenCalledWith({
            [form_price_book_id]: priceBookId,
            [form_price_book_id + '__r']: priceBookId__r
        })
        expect(spy).toHaveBeenCalled();
        spy.mockRestore();

    })
    test('todoAfterFieldChange 校验价目表不通过', async () => {
        // 校验价目表不通过
        spy_validPriceBookByCustomer.mockImplementation(() => {
            return {
                result: false
            }
        })
        await NoClearMd.todoAfterFieldChange(field, param, pluginService, resolve)
        expect(spy_confirm).toHaveBeenCalledWith(expect.objectContaining({
            msg: '更换{{name}}将清空已选价目表和产品确认更换'
        }))
    })
    test('todoAfterFieldChange 开价格政策', async () => {
        // 开价格政策
        const spy_pluginService = jest.spyOn(pluginService.api, 'getPlugins');
        spy_pluginService.mockImplementation(() => {
            return [{
                pluginApiName: 'price_policy'
            }]
        })
        const spy_checkMdHasData = jest.fn()
        spy_checkMdHasData.mockReturnValueOnce(true)
        await NoClearMd.todoAfterFieldChange(field, param, pluginService, resolve)
        expect(spy_confirm).toHaveBeenCalled();
        spy_pluginService.mockRestore()
    })
    test('todoAfterFieldChange 高新投三江 ui 事件不触发业务', async () => {
        param.isManual = false;
        global.CRM.ea = '782635_sandbox'
        let r = await NoClearMd.todoAfterFieldChange(field, param, pluginService, resolve)
        expect(r).toBeUndefined()
    })
    test('_dataChange_after 切换其他字段', async () => {
        let _param = {
            ...param,
            changeData: {
                new_opportunity_id: ''
            },
            oldData: {
                "new_opportunity_id": "6656ec90bff7980007f11bb0",
                "new_opportunity_id__r": "1"
            }
        }
        const spy_todoAfterFieldChange = jest.spyOn(NoClearMd, 'todoAfterFieldChange')
        let r = await NoClearMd._dataChange_after(pluginService, _param);
        expect(spy_todoAfterFieldChange).not.toHaveBeenCalled()
        expect(r).toBeUndefined()

    })
})

// 新增测试用例 - 测试未覆盖的方法
describe('plays validPriceBookByCustomer', () => {
    test('validPriceBookByCustomer 正常情况', async () => {
        PS.request = jest.fn().mockResolvedValue({result: true});
        let result = await NoClearMd.validPriceBookByCustomer('customer123', 'pricebook123');
        expect(result.result).toBe(true);
        expect(PS.request).toHaveBeenCalled();
    })
})

describe('plays getDefaultPriceBook', () => {
    test('getDefaultPriceBook', async () => {
        PS.request = jest.fn().mockResolvedValue({
            result: {
                pricebook_id: 'default123',
                pricebook_id__r: '默认价目表'
            }
        });
        let result = await NoClearMd.getDefaultPriceBook('customer123');
        expect(result.pricebook_id).toBe('default123');
        expect(PS.request).toHaveBeenCalled();
    })
})

describe('plays clearMdData', () => {
    test('clearMdData', () => {
        let testParam = {
            dataUpdater: {
                clearDetail: jest.fn()
            },
            objApiName: mdApiName
        };
        NoClearMd.clearMdData(testParam);
        expect(testParam.dataUpdater.clearDetail).toHaveBeenCalledWith(mdApiName);
    })
})

describe('plays _beforeRender', () => {
    test('_beforeRender', () => {
        let testParam = {
            dataGetter: {
                getDescribe: jest.fn().mockReturnValue({
                    fields: {
                        price_book_id: {
                            label: '价目表',
                            is_required: true
                        }
                    }
                })
            }
        };
        NoClearMd._beforeRender({}, testParam);
        expect(NoClearMd.masterPriceBookDesc).toEqual({
            label: '价目表',
            is_required: true
        });
    })
})

describe('plays checkMdIsMatchCustomer', () => {
    test('checkMdIsMatchCustomer 价目表校验通过', async () => {
        spy_validPriceBookByCustomer.mockImplementation(() => {
            return Promise.resolve({result: true})
        })

        let testParam = {
            dataGetter: {
                getMasterData: jest.fn().mockReturnValue({
                    account_id: 'customer123',
                    price_book_id: 'pricebook123'
                })
            }
        };

        let result = await NoClearMd.checkMdIsMatchCustomer(testParam, pluginService);
        expect(result).toBe(true);
    })

    test('checkMdIsMatchCustomer 价目表校验失败', async () => {
        spy_validPriceBookByCustomer.mockImplementation(() => {
            return Promise.resolve({result: false})
        })

        let testParam = {
            dataGetter: {
                getMasterData: jest.fn().mockReturnValue({
                    account_id: 'customer123',
                    price_book_id: 'pricebook123'
                })
            }
        };

        let result = await NoClearMd.checkMdIsMatchCustomer(testParam, pluginService);
        expect(result).toBe(false);
    })
})

describe('plays getMasterFields', () => {
    test('getMasterFields', () => {
        let result = NoClearMd.getMasterFields();
        expect(result).toHaveProperty('form_account_id');
        expect(result).toHaveProperty('form_partner_id');
        expect(result).toHaveProperty('form_price_book_id');
    })
})

describe('plays getAllFields', () => {
    test('getAllFields', () => {
        let result = NoClearMd.getAllFields(mdApiName);
        expect(result).toHaveProperty('product_id');
        expect(result).toHaveProperty('price_book_id');
        expect(result).toHaveProperty('quantity');
    })
})

describe('plays getConfig', () => {
    test('getConfig', () => {
        let result = NoClearMd.getConfig('openPriceList');
        expect(result).toBe(true);
    })
})